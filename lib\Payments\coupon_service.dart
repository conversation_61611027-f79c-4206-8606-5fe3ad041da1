import 'package:v18ui/payments/payment_models.dart';

/// Service to manage coupons
class CouponService {
  static final CouponService _instance = CouponService._internal();
  
  factory CouponService() => _instance;
  
  CouponService._internal();

  /// Get available coupons (placeholder - replace with actual API call)
  List<Coupon> getAvailableCoupons() {
    // TODO: Replace with actual API call to get coupons
    return [
      Coupon(
        code: 'WELCOME10',
        description: 'Welcome offer - 10% off',
        discountPercentage: 10.0,
        minimumAmount: 0.0,
        expiryDate: DateTime.now().add(const Duration(days: 30)),
      ),
      Coupon(
        code: 'SAVE20',
        description: 'Save 20% on your subscription',
        discountPercentage: 20.0,
        minimumAmount: 50.0,
        expiryDate: DateTime.now().add(const Duration(days: 15)),
      ),
      Coupon(
        code: 'FLAT50',
        description: 'Flat ₹50 off',
        discountPercentage: 0.0,
        discountAmount: 50.0,
        minimumAmount: 100.0,
        expiryDate: DateTime.now().add(const Duration(days: 7)),
      ),
    ];
  }

  /// Validate coupon code
  Future<Coupon?> validateCoupon(String code) async {
    try {
      // TODO: Replace with actual API call to validate coupon
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      final coupons = getAvailableCoupons();
      final coupon = coupons.where((c) => c.code.toUpperCase() == code.toUpperCase()).firstOrNull;
      
      if (coupon != null && coupon.isValid) {
        return coupon;
      }
      
      return null;
    } catch (e) {
      print('❌ Error validating coupon: $e');
      return null;
    }
  }

  /// Apply coupon to amount
  Map<String, dynamic> applyCoupon(Coupon coupon, double originalAmount) {
    final discount = coupon.calculateDiscount(originalAmount);
    final finalAmount = coupon.calculateFinalAmount(originalAmount);
    
    return {
      'originalAmount': originalAmount,
      'discount': discount,
      'finalAmount': finalAmount,
      'coupon': coupon,
      'isApplied': discount > 0,
    };
  }

  /// Get coupon display text
  String getCouponDisplayText(Coupon coupon) {
    if (coupon.discountAmount != null) {
      return 'Flat ₹${coupon.discountAmount!.toStringAsFixed(0)} off';
    } else {
      return '${coupon.discountPercentage.toStringAsFixed(0)}% off';
    }
  }

  /// Check if coupon is applicable for amount
  bool isCouponApplicable(Coupon coupon, double amount) {
    return coupon.isValid && amount >= coupon.minimumAmount;
  }

  /// Get error message for invalid coupon
  String getInvalidCouponMessage(String code, double amount) {
    final coupons = getAvailableCoupons();
    final coupon = coupons.where((c) => c.code.toUpperCase() == code.toUpperCase()).firstOrNull;
    
    if (coupon == null) {
      return 'Invalid coupon code';
    }
    
    if (!coupon.isActive) {
      return 'This coupon is no longer active';
    }
    
    if (coupon.expiryDate != null && DateTime.now().isAfter(coupon.expiryDate!)) {
      return 'This coupon has expired';
    }
    
    if (amount < coupon.minimumAmount) {
      return 'Minimum amount ₹${coupon.minimumAmount.toStringAsFixed(0)} required for this coupon';
    }
    
    return 'Coupon cannot be applied';
  }
}
