/// Enum for payment status
enum PaymentStatus {
  pending,
  success,
  failed,
  cancelled,
}

/// Model for payment result
class PaymentResult {
  final PaymentStatus status;
  final String? transactionId;
  final String message;
  final Map<String, dynamic>? additionalData;

  PaymentResult({
    required this.status,
    this.transactionId,
    required this.message,
    this.additionalData,
  });

  factory PaymentResult.fromJson(Map<String, dynamic> json) {
    return PaymentResult(
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.failed,
      ),
      transactionId: json['transactionId'],
      message: json['message'] ?? '',
      additionalData: json['additionalData'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status.toString().split('.').last,
      'transactionId': transactionId,
      'message': message,
      'additionalData': additionalData,
    };
  }

  bool get isSuccess => status == PaymentStatus.success;
  bool get isPending => status == PaymentStatus.pending;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isCancelled => status == PaymentStatus.cancelled;
}

/// Model for subscription plans
class SubscriptionPlan {
  final String id;
  final String name;
  final String description;
  final double price;
  final int durationInDays;
  final List<String> features;
  final bool isPopular;

  SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.durationInDays,
    required this.features,
    this.isPopular = false,
  });

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlan(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      price: (json['price'] as num).toDouble(),
      durationInDays: json['durationInDays'],
      features: List<String>.from(json['features'] ?? []),
      isPopular: json['isPopular'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'durationInDays': durationInDays,
      'features': features,
      'isPopular': isPopular,
    };
  }

  String get formattedPrice => '₹${price.toStringAsFixed(0)}';
  String get durationText {
    if (durationInDays == 30) return '1 Month';
    if (durationInDays == 90) return '3 Months';
    if (durationInDays == 365) return '1 Year';
    return '$durationInDays Days';
  }
}

/// Model for coupon
class Coupon {
  final String code;
  final String description;
  final double discountPercentage;
  final double? discountAmount;
  final double minimumAmount;
  final bool isActive;
  final DateTime? expiryDate;

  Coupon({
    required this.code,
    required this.description,
    required this.discountPercentage,
    this.discountAmount,
    this.minimumAmount = 0.0,
    this.isActive = true,
    this.expiryDate,
  });

  factory Coupon.fromJson(Map<String, dynamic> json) {
    return Coupon(
      code: json['code'],
      description: json['description'],
      discountPercentage: (json['discountPercentage'] as num).toDouble(),
      discountAmount: json['discountAmount'] != null
          ? (json['discountAmount'] as num).toDouble()
          : null,
      minimumAmount: (json['minimumAmount'] as num?)?.toDouble() ?? 0.0,
      isActive: json['isActive'] ?? true,
      expiryDate: json['expiryDate'] != null
          ? DateTime.parse(json['expiryDate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'description': description,
      'discountPercentage': discountPercentage,
      'discountAmount': discountAmount,
      'minimumAmount': minimumAmount,
      'isActive': isActive,
      'expiryDate': expiryDate?.toIso8601String(),
    };
  }

  bool get isValid {
    if (!isActive) return false;
    if (expiryDate != null && DateTime.now().isAfter(expiryDate!)) return false;
    return true;
  }

  double calculateDiscount(double amount) {
    if (!isValid || amount < minimumAmount) return 0.0;

    if (discountAmount != null) {
      return discountAmount!;
    }

    return (amount * discountPercentage) / 100;
  }

  double calculateFinalAmount(double originalAmount) {
    final discount = calculateDiscount(originalAmount);
    return originalAmount - discount;
  }
}

/// Model for payment transaction
class PaymentTransaction {
  final String id;
  final String orderId;
  final String planId;
  final double amount;
  final PaymentStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? transactionId;
  final String? failureReason;

  PaymentTransaction({
    required this.id,
    required this.orderId,
    required this.planId,
    required this.amount,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.transactionId,
    this.failureReason,
  });

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) {
    return PaymentTransaction(
      id: json['id'],
      orderId: json['orderId'],
      planId: json['planId'],
      amount: (json['amount'] as num).toDouble(),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.failed,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'])
          : null,
      transactionId: json['transactionId'],
      failureReason: json['failureReason'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'orderId': orderId,
      'planId': planId,
      'amount': amount,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'transactionId': transactionId,
      'failureReason': failureReason,
    };
  }
}
