import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/config.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Screens/porn_deaddiction_mode_screen.dart';
import 'package:v18ui/Screens/focused_youtube_screen.dart';
import 'package:v18ui/Screens/safe_app_list.dart';
import 'package:v18ui/Services/porn_deaddiction_service.dart';
import 'package:v18ui/Services/subscription_service.dart';
import 'package:v18ui/main.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with RouteAware {
  List<DashboardItem> dashboardItems = [];

  @override
  void initState() {
    super.initState();
    // unsetting isFirstRun after successfully landing in HomeScreen in first run
    if (ConfigManager().config!.isFirstRun == true) {
      ConfigManager().config!.isFirstRun = false;
      Config.saveConfig(ConfigManager().config!);
    }
    dashboardItems = loadDashBoardItems();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Subscribe this route to the observer
    AppHandler.routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    // Always unsubscribe
    AppHandler.routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    // Called when returning to this screen
    print("Coming back - refreshing data");
    setState(() {
      dashboardItems = loadDashBoardItems();
    });
  }

  List<DashboardItem> loadDashBoardItems() {
    List<DashboardItem> dashboardItems = [
      DashboardItem(
          title: 'Porn Deaddiction Mode',
          iconPath: 'assets/icons/porn_deaddiction_mode.png',
          toggleValue: PornDeaddictionService()
              .pornDeaddictionServiceData!
              .serviceActive!,
          onTap: (context) async {
            // Check subscription before proceeding
            bool canProceed = await SubscriptionService()
                .checkSubscriptionAndShowDialog(
                    context, 'Porn Deaddiction Mode');

            if (!canProceed) return;

            if (PornDeaddictionService()
                .pornDeaddictionServiceData!
                .serviceActive!) {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => PornDeaddictionModeScreen()),
              );
            } else {
              await PornDeaddictionService().activateService(context);
            }
          },
          serviceActive: PornDeaddictionService()
              .pornDeaddictionServiceData!
              .serviceActive!,
          serviceLockIconRequired: true),
      DashboardItem(
          title: 'Quit Gambling',
          iconPath: 'assets/icons/gambling_icon.png',
          toggleValue: false, // ConfigManager().services!.quitGamblingService,
          onTap: (context) async {
            // Check subscription before proceeding
            bool canProceed = await SubscriptionService()
                .checkSubscriptionAndShowDialog(context, 'Quit Gambling');

            if (!canProceed) return;

            print('Gambling Mode tapped');
          },
          serviceActive: false,
          serviceLockIconRequired: true),
      DashboardItem(
          title: 'Control Entertainment Time',
          iconPath: 'assets/icons/entertainment_icon.png',
          onTap: (context) async {
            // Check subscription before proceeding
            bool canProceed = await SubscriptionService()
                .checkSubscriptionAndShowDialog(
                    context, 'Control Entertainment Time');

            if (!canProceed) return;

            print("Control Entertainment Time tapped");
          },
          toggleValue: false,
          serviceActive: false,
          serviceLockIconRequired: true),
      DashboardItem(
          title: 'Block Adult Sites',
          iconPath: 'assets/icons/adult_icon.png',
          onTap: (context) async {
            // Check subscription before proceeding
            bool canProceed = await SubscriptionService()
                .checkSubscriptionAndShowDialog(context, 'Block Adult Sites');

            if (!canProceed) return;

            print("Block Adult Sites tapped");
          },
          toggleValue: false,
          serviceActive: false,
          serviceLockIconRequired: true),
      DashboardItem(
          title: 'Control Gaming Time',
          iconPath: 'assets/icons/gaming_icon.png',
          onTap: (context) async {
            // Check subscription before proceeding
            bool canProceed = await SubscriptionService()
                .checkSubscriptionAndShowDialog(context, 'Control Gaming Time');

            if (!canProceed) return;

            print("Control Gaming Time tapped");
          },
          toggleValue: false,
          serviceActive: false,
          serviceLockIconRequired: true),
      DashboardItem(
          title: 'Block App',
          iconPath: 'assets/icons/block_apps_icon.png',
          onTap: (context) async {
            // Check subscription before proceeding
            bool canProceed = await SubscriptionService()
                .checkSubscriptionAndShowDialog(context, 'Block App');

            if (!canProceed) return;

            print("Block App tapped");
          },
          toggleValue: false,
          serviceActive: false,
          serviceLockIconRequired: true),
      DashboardItem(
          title: 'Focused YouTube',
          iconPath: 'assets/icons/focused_youtube.png',
          onTap: (context) {
            Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const FocusedYouTubeApp()),
            );
          },
          serviceActive: false,
          serviceLockIconRequired: false),
      DashboardItem(
          title: 'Make your own distraction free zone ',
          iconPath: 'assets/icons/custom_blocking_icon.png',
          onTap: (context) async {
            // Check subscription before proceeding
            bool canProceed = await SubscriptionService()
                .checkSubscriptionAndShowDialog(
                    context, 'Make your own distraction free zone');

            if (!canProceed) return;

            print("Custom blocking clicked");
          },
          serviceActive: false,
          serviceLockIconRequired: true),
    ];
    return dashboardItems;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("V18"),
        backgroundColor: Colors.lightBlue.shade200,
        centerTitle: true,
      ),
      drawer: Drawer(
          child: ListView(
        padding: EdgeInsets.only(
            top:
                100), // This is a temporary fix , drawer does not look good , modify the UI later
        children: [
          _buildDrawerItem(Icons.check_circle, "Safe App List", () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => SafeAppList()),
            );
          }),
          _buildDrawerItem(Icons.support, "Contact Support", () {}),
          _buildDrawerItem(Icons.star, "Rate Us", () {}),
          _buildDrawerItem(Icons.share, "Refer a Friend", () {}),
          _buildDrawerItem(Icons.card_membership, "Your Subscription", () {}),
        ],
      )),
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: ListView.builder(
          itemCount: dashboardItems.length,
          padding: EdgeInsets.symmetric(vertical: 12),
          itemBuilder: (context, index) {
            final item = dashboardItems[index];
            return DashboardTile(item: item);
          },
        ),
      ),
    );
  }

  /// Drawer Item
  Widget _buildDrawerItem(IconData icon, String title, Function onTap) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title, style: TextStyle(fontSize: 16)),
      onTap: () => onTap(),
    );
  }
}

class DashboardTile extends StatefulWidget {
  DashboardItem item;

  DashboardTile({super.key, required this.item});

  @override
  State<DashboardTile> createState() => _DashboardTileState();
}

class _DashboardTileState extends State<DashboardTile> {
  @override
  Widget build(BuildContext context) {
    return Material(
        child: InkWell(
            onTap: () {
              widget.item.onTap?.call(context);
            },
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 8, horizontal: 15),
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFFEEF2F3), Color(0xFFDDEAF5)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 12,
                    offset: Offset(0, 6),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        widget.item.iconPath,
                        width: 56,
                        height: 56,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      widget.item.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                  if (widget.item.serviceLockIconRequired)
                    IconButton(
                      icon: Icon(
                        widget.item.serviceActive
                            ? Icons.lock
                            : Icons.lock_open,
                        color: Colors.grey[700],
                      ),
                      onPressed: () async {
                        // Check subscription before allowing lock/unlock
                        bool canProceed = await SubscriptionService()
                            .checkSubscriptionAndShowDialog(
                                context, widget.item.title);

                        if (!canProceed) return;

                        setState(() {
                          widget.item.serviceActive =
                              !widget.item.serviceActive;
                        });
                      },
                    ),
                  if (widget.item.toggleValue != null)
                    Switch(
                      value: widget.item.toggleValue!,
                      onChanged: (val) async {
                        // Check subscription before allowing toggle (except for Focused YouTube)
                        if (widget.item.serviceLockIconRequired) {
                          bool canProceed = await SubscriptionService()
                              .checkSubscriptionAndShowDialog(
                                  context, widget.item.title);

                          if (!canProceed) return;
                        }

                        widget.item.onTap?.call(context);
                        setState(() {});
                      },
                      activeColor: Colors.blueAccent,
                    )
                ],
              ),
            )));
  }
}

class DashboardItem {
  final String title;
  final String iconPath;
  final String? subtitle;
  Function(BuildContext context)? onTap;
  bool? toggleValue;
  bool serviceActive;
  bool serviceLockIconRequired;

  DashboardItem(
      {required this.title,
      required this.iconPath,
      this.subtitle,
      this.onTap,
      this.toggleValue,
      required this.serviceActive,
      required this.serviceLockIconRequired});
}
