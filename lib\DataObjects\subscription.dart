enum SubscriptionStatus {
  active,
  inactive,
  expired,
}

class Subscription {
  final String status;
  final String expiryDate;

  Subscription({
    required this.status,
    required this.expiryDate,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      status: json['status'],
      expiryDate: json['expiry_date'],
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'expiry_date': expiryDate,
      };

  /// Check if subscription is active
  bool get isActive => status.toLowerCase() == 'active';

  /// Check if subscription is expired
  bool get isExpired => status.toLowerCase() == 'expired';

  /// Check if subscription is inactive
  bool get isInactive => status.toLowerCase() == 'inactive';

  /// Get subscription status as enum
  SubscriptionStatus get subscriptionStatus {
    switch (status.toLowerCase()) {
      case 'active':
        return SubscriptionStatus.active;
      case 'expired':
        return SubscriptionStatus.expired;
      case 'inactive':
      default:
        return SubscriptionStatus.inactive;
    }
  }
}
