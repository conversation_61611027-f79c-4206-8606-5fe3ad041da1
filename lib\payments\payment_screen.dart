import 'package:flutter/material.dart';
import 'package:v18ui/payments/payment_models.dart';
import 'package:v18ui/payments/subscription_plans_service.dart';
import 'package:v18ui/payments/upi_payment_service.dart';
import 'package:v18ui/payments/payment_processing_screen.dart';
import 'package:v18ui/payments/coupon_service.dart';
import 'package:v18ui/DataObjects/subscription.dart';

class PaymentScreen extends StatefulWidget {
  final SubscriptionStatus currentStatus;

  const PaymentScreen({
    super.key,
    required this.currentStatus,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final SubscriptionPlansService _plansService = SubscriptionPlansService();
  final CouponService _couponService = CouponService();
  final TextEditingController _couponController = TextEditingController();

  SubscriptionPlan? _selectedPlan;
  Coupon? _appliedCoupon;
  bool _isLoading = false;
  bool _isCouponLoading = false;
  String? _couponError;

  @override
  void initState() {
    super.initState();
    // Pre-select the most popular plan
    _selectedPlan = _plansService.getMostPopularPlan();
  }

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final plans = _plansService.getAvailablePlans();

    return Scaffold(
      appBar: AppBar(
        title: Text(_getScreenTitle()),
        backgroundColor: Colors.blue.shade100,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildHeader(),
                        const SizedBox(height: 24),
                        _buildPlansList(plans),
                        const SizedBox(height: 24),
                        if (_selectedPlan != null) _buildSelectedPlanDetails(),
                        const SizedBox(height: 24),
                        if (_selectedPlan != null) _buildCouponSection(),
                      ],
                    ),
                  ),
                ),
                _buildBottomSection(),
              ],
            ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getHeaderTitle(),
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _getHeaderSubtitle(),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlansList(List<SubscriptionPlan> plans) {
    // For single plan, show simplified UI
    if (plans.length == 1) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Subscription Plan',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildSinglePlanCard(plans.first),
        ],
      );
    }

    // For multiple plans (fallback)
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Choose Your Plan',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...plans.map((plan) => _buildPlanCard(plan)),
      ],
    );
  }

  Widget _buildSinglePlanCard(SubscriptionPlan plan) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        border: Border.all(color: Colors.blue, width: 2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      plan.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      plan.description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    plan.formattedPrice,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  Text(
                    plan.durationText,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCard(SubscriptionPlan plan) {
    final isSelected = _selectedPlan?.id == plan.id;
    final savings = _plansService.getSavingsText(plan);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => setState(() => _selectedPlan = plan),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected ? Colors.blue.shade50 : Colors.white,
            border: Border.all(
              color: isSelected ? Colors.blue : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Radio<SubscriptionPlan>(
                value: plan,
                groupValue: _selectedPlan,
                onChanged: (value) => setState(() => _selectedPlan = value),
                activeColor: Colors.blue,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          plan.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (plan.isPopular) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              'POPULAR',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    Text(
                      plan.description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    if (savings.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        savings,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    plan.formattedPrice,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  Text(
                    plan.durationText,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedPlanDetails() {
    if (_selectedPlan == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'What you get:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ..._selectedPlan!.features.map(
            (feature) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      feature,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCouponSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Apply Coupon',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _couponController,
                  decoration: InputDecoration(
                    hintText: 'Enter coupon code',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 12,
                    ),
                    errorText: _couponError,
                  ),
                  textCapitalization: TextCapitalization.characters,
                  onChanged: (value) {
                    if (_couponError != null) {
                      setState(() => _couponError = null);
                    }
                  },
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: _isCouponLoading ? null : _applyCoupon,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
                child: _isCouponLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Apply',
                        style: TextStyle(color: Colors.white),
                      ),
              ),
            ],
          ),
          if (_appliedCoupon != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle,
                      color: Colors.green.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Coupon Applied: ${_appliedCoupon!.code}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                        Text(
                          _appliedCoupon!.description,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.green.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: _removeCoupon,
                    icon: Icon(Icons.close,
                        color: Colors.green.shade600, size: 20),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    final plans = _plansService.getAvailablePlans();
    final isSinglePlan = plans.length == 1;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade300,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          if (_selectedPlan != null) ...[
            // Show pricing breakdown with coupon
            if (_appliedCoupon != null) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Original Amount:'),
                  Text(
                    _selectedPlan!.formattedPrice,
                    style: const TextStyle(
                      decoration: TextDecoration.lineThrough,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Discount (${_appliedCoupon!.code}):'),
                  Text(
                    '-₹${_appliedCoupon!.calculateDiscount(_selectedPlan!.price).toStringAsFixed(0)}',
                    style: const TextStyle(color: Colors.green),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              const Divider(),
              const SizedBox(height: 4),
            ],
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _appliedCoupon != null
                      ? 'Final Amount:'
                      : (isSinglePlan ? 'Amount:' : 'Total Amount:'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _getFinalPrice(),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _selectedPlan != null ? _proceedToPayment : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                isSinglePlan ? 'Subscribe with UPI' : 'Pay with UPI',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getScreenTitle() {
    switch (widget.currentStatus) {
      case SubscriptionStatus.expired:
        return 'Renew Subscription';
      case SubscriptionStatus.inactive:
      default:
        return 'Subscription';
    }
  }

  String _getHeaderTitle() {
    switch (widget.currentStatus) {
      case SubscriptionStatus.expired:
        return 'Your subscription has expired';
      case SubscriptionStatus.inactive:
      default:
        return 'Unlock Premium Features';
    }
  }

  String _getHeaderSubtitle() {
    switch (widget.currentStatus) {
      case SubscriptionStatus.expired:
        return 'Renew your subscription to continue using all premium features.';
      case SubscriptionStatus.inactive:
      default:
        return 'Get access to all premium features and take control of your digital habits.';
    }
  }

  String _getFinalPrice() {
    if (_selectedPlan == null) return '₹0';

    if (_appliedCoupon != null) {
      final finalAmount =
          _appliedCoupon!.calculateFinalAmount(_selectedPlan!.price);
      return '₹${finalAmount.toStringAsFixed(0)}';
    }

    return _selectedPlan!.formattedPrice;
  }

  double _getFinalAmount() {
    if (_selectedPlan == null) return 0.0;

    if (_appliedCoupon != null) {
      return _appliedCoupon!.calculateFinalAmount(_selectedPlan!.price);
    }

    return _selectedPlan!.price;
  }

  void _applyCoupon() async {
    final code = _couponController.text.trim();
    if (code.isEmpty) return;

    setState(() {
      _isCouponLoading = true;
      _couponError = null;
    });

    try {
      final coupon = await _couponService.validateCoupon(code);

      if (coupon != null && _selectedPlan != null) {
        if (_couponService.isCouponApplicable(coupon, _selectedPlan!.price)) {
          setState(() {
            _appliedCoupon = coupon;
            _couponController.clear();
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Coupon applied successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          setState(() {
            _couponError = _couponService.getInvalidCouponMessage(
                code, _selectedPlan!.price);
          });
        }
      } else {
        setState(() {
          _couponError = 'Invalid coupon code';
        });
      }
    } catch (e) {
      setState(() {
        _couponError = 'Error applying coupon';
      });
    } finally {
      setState(() => _isCouponLoading = false);
    }
  }

  void _removeCoupon() {
    setState(() {
      _appliedCoupon = null;
      _couponController.clear();
      _couponError = null;
    });
  }

  void _proceedToPayment() async {
    if (_selectedPlan == null) return;

    setState(() => _isLoading = true);

    try {
      // Generate order ID
      final orderId = UpiPaymentService.generateOrderId();
      final finalAmount = _getFinalAmount();

      // Navigate to payment processing screen with final amount
      final result = await Navigator.push<PaymentResult>(
        context,
        MaterialPageRoute(
          builder: (context) => PaymentProcessingScreen(
            plan: _selectedPlan!,
            orderId: orderId,
            finalAmount: finalAmount,
            appliedCoupon: _appliedCoupon,
          ),
        ),
      );

      if (result != null && result.isSuccess) {
        // Payment successful - close this screen
        Navigator.pop(context, result);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
