import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/DataObjects/subscription.dart';
import 'package:v18ui/Widgets/subscription_dialog.dart';

/// Service to handle subscription validation and dialogs
class SubscriptionService {
  static final SubscriptionService _instance = SubscriptionService._internal();
  
  factory SubscriptionService() => _instance;
  
  SubscriptionService._internal();

  /// Check if user has active subscription
  bool get hasActiveSubscription {
    final config = ConfigManager().config;
    if (config == null) return false;
    return config.subscription.isActive;
  }

  /// Check if subscription is expired
  bool get isSubscriptionExpired {
    final config = ConfigManager().config;
    if (config == null) return false;
    return config.subscription.isExpired;
  }

  /// Check if subscription is inactive
  bool get isSubscriptionInactive {
    final config = ConfigManager().config;
    if (config == null) return true;
    return config.subscription.isInactive;
  }

  /// Get current subscription status
  SubscriptionStatus get subscriptionStatus {
    final config = ConfigManager().config;
    if (config == null) return SubscriptionStatus.inactive;
    return config.subscription.subscriptionStatus;
  }

  /// Check subscription and show dialog if needed
  /// Returns true if user can proceed, false if subscription is required
  Future<bool> checkSubscriptionAndShowDialog(BuildContext context, String serviceName) async {
    // Skip check for Focused YouTube as it's free
    if (serviceName.toLowerCase().contains('focused youtube')) {
      return true;
    }

    if (hasActiveSubscription) {
      return true;
    }

    // Show subscription dialog
    await _showSubscriptionDialog(context);
    return false;
  }

  /// Show subscription dialog based on current status
  Future<void> _showSubscriptionDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return SubscriptionDialog(
          subscriptionStatus: subscriptionStatus,
        );
      },
    );
  }

  /// Validate subscription for service activation
  /// This method can be called before activating any service
  bool validateSubscriptionForService(String serviceName) {
    // Skip validation for Focused YouTube
    if (serviceName.toLowerCase().contains('focused youtube')) {
      return true;
    }
    
    return hasActiveSubscription;
  }

  /// Get subscription message based on status
  String getSubscriptionMessage() {
    switch (subscriptionStatus) {
      case SubscriptionStatus.expired:
        return "Your subscription has expired, please re-activate your subscription";
      case SubscriptionStatus.inactive:
      default:
        return "Please activate your subscription to use this service";
    }
  }

  /// Get subscription dialog title based on status
  String getSubscriptionDialogTitle() {
    switch (subscriptionStatus) {
      case SubscriptionStatus.expired:
        return "Subscription Expired";
      case SubscriptionStatus.inactive:
      default:
        return "Subscription Required";
    }
  }
}
