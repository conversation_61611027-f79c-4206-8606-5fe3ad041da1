import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:v18ui/payments/payment_models.dart';

/// Service to handle UPI payments
class UpiPaymentService {
  static const MethodChannel _channel =
      MethodChannel('com.example.v18ui/upi_payment');

  // TODO: Replace with actual payment details
  static const String _merchantId = "YOUR_MERCHANT_ID";
  static const String _merchantName = "V18 App";
  static const String _upiId = "9833562091@kotak"; // Replace with your UPI ID
  static const String _transactionNote = "V18 App Subscription";

  /// Initiate UPI payment
  static Future<PaymentResult> initiatePayment({
    required double amount,
    required String orderId,
    String? customerName,
    String? customerEmail,
  }) async {
    try {
      // Create UPI payment URL
      final upiUrl = _createUpiUrl(
        amount: amount,
        orderId: orderId,
        customerName: customerName,
      );

      print('🔄 Initiating UPI payment with URL: $upiUrl');

      // Try to launch UPI app
      final bool launched = await _launchUpiApp(upiUrl);

      if (launched) {
        // Return pending status - actual verification will happen later
        return PaymentResult(
          status: PaymentStatus.pending,
          transactionId: orderId,
          message:
              "Payment initiated. Please complete the payment in your UPI app.",
        );
      } else {
        return PaymentResult(
          status: PaymentStatus.failed,
          message:
              "No UPI app found. Please install a UPI app like PhonePe, Google Pay, or Paytm.",
        );
      }
    } catch (e) {
      print('❌ UPI Payment Error: $e');
      return PaymentResult(
        status: PaymentStatus.failed,
        message: "Payment failed: ${e.toString()}",
      );
    }
  }

  /// Create UPI payment URL
  static String _createUpiUrl({
    required double amount,
    required String orderId,
    String? customerName,
  }) {
    final Map<String, String> params = {
      'pa': _upiId, // Payee address (UPI ID)
      'pn': _merchantName, // Payee name
      'mc': '0000', // Merchant category code (0000 for general)
      'tr': orderId, // Transaction reference ID
      'tn': _transactionNote, // Transaction note
      'am': amount.toStringAsFixed(2), // Amount
      'cu': 'INR', // Currency
    };

    // Add customer name if provided
    if (customerName != null && customerName.isNotEmpty) {
      params['tn'] = '$_transactionNote - $customerName';
    }

    // Build UPI URL
    final queryString = params.entries
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return 'upi://pay?$queryString';
  }

  /// Launch UPI app
  static Future<bool> _launchUpiApp(String upiUrl) async {
    try {
      final Uri uri = Uri.parse(upiUrl);

      // Check if any UPI app can handle this URL
      if (await canLaunchUrl(uri)) {
        return await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      }

      return false;
    } catch (e) {
      print('❌ Error launching UPI app: $e');
      return false;
    }
  }

  /// Verify payment status (placeholder - implement with your payment gateway)
  static Future<PaymentResult> verifyPayment(String transactionId) async {
    try {
      // TODO: Implement actual payment verification with your payment gateway
      // This is a placeholder implementation

      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      // For now, return a mock successful result
      // In real implementation, you would call your backend API to verify the payment
      return PaymentResult(
        status: PaymentStatus.success,
        transactionId: transactionId,
        message: "Payment verified successfully",
      );
    } catch (e) {
      print('❌ Payment verification error: $e');
      return PaymentResult(
        status: PaymentStatus.failed,
        transactionId: transactionId,
        message: "Payment verification failed: ${e.toString()}",
      );
    }
  }

  /// Get available UPI apps on device
  static Future<List<String>> getAvailableUpiApps() async {
    try {
      // TODO: Implement method to check available UPI apps
      // This is a placeholder list
      return [
        'PhonePe',
        'Google Pay',
        'Paytm',
        'BHIM',
        'Amazon Pay',
      ];
    } catch (e) {
      print('❌ Error getting UPI apps: $e');
      return [];
    }
  }

  /// Generate unique order ID
  static String generateOrderId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'V18_${timestamp}_${(timestamp % 10000).toString().padLeft(4, '0')}';
  }
}
