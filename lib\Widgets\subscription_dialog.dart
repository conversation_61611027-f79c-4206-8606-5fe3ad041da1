import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/subscription.dart';

/// Dialog widget to show subscription-related messages
class SubscriptionDialog extends StatelessWidget {
  final SubscriptionStatus subscriptionStatus;

  const SubscriptionDialog({
    super.key,
    required this.subscriptionStatus,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_getDialogTitle()),
      content: Text(_getDialogMessage()),
      actions: <Widget>[
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
          ),
          onPressed: () {
            Navigator.of(context).pop();
            // TODO: Navigate to subscription/payment screen
            _handleSubscriptionAction(context);
          },
          child: Text(_getActionButtonText()),
        ),
      ],
    );
  }

  /// Get dialog title based on subscription status
  String _getDialogTitle() {
    switch (subscriptionStatus) {
      case SubscriptionStatus.expired:
        return "Subscription Expired";
      case SubscriptionStatus.inactive:
      default:
        return "Subscription Required";
    }
  }

  /// Get dialog message based on subscription status
  String _getDialogMessage() {
    switch (subscriptionStatus) {
      case SubscriptionStatus.expired:
        return "Your subscription has expired, please re-activate your subscription to continue using this service.";
      case SubscriptionStatus.inactive:
      default:
        return "Please activate your subscription to use this service. Focused YouTube is available for free without subscription.";
    }
  }

  /// Get action button text based on subscription status
  String _getActionButtonText() {
    switch (subscriptionStatus) {
      case SubscriptionStatus.expired:
        return "Re-activate";
      case SubscriptionStatus.inactive:
      default:
        return "Activate";
    }
  }

  /// Handle subscription action (navigate to payment/subscription screen)
  void _handleSubscriptionAction(BuildContext context) {
    // TODO: Implement navigation to subscription/payment screen
    // For now, just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Subscription activation will be implemented soon'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
