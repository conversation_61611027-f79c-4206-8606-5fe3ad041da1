import 'package:v18ui/payments/payment_models.dart';

/// Service to manage subscription plans
class SubscriptionPlansService {
  static final SubscriptionPlansService _instance =
      SubscriptionPlansService._internal();

  factory SubscriptionPlansService() => _instance;

  SubscriptionPlansService._internal();

  /// Get available subscription plans
  List<SubscriptionPlan> getAvailablePlans() {
    // TODO: Replace with actual plans and pricing
    return [
      SubscriptionPlan(
        id: 'monthly',
        name: 'Monthly Plan',
        description: 'Perfect for trying out our services',
        price: 1.0, // TODO: Replace with actual price
        durationInDays: 30,
        features: [
          'All premium features',
          'Porn Deaddiction Mode',
          'Block Adult Sites',
          'Control Gaming Time',
          'Control Entertainment Time',
          'Block Apps',
          'Custom Blocking',
          'Priority Support',
        ],
        isPopular: false,
      ),
      SubscriptionPlan(
        id: 'quarterly',
        name: '3 Months Plan',
        description: 'Most popular choice with great savings',
        price: 2.0, // TODO: Replace with actual price
        durationInDays: 90,
        features: [
          'All premium features',
          'Porn Deaddiction Mode',
          'Block Adult Sites',
          'Control Gaming Time',
          'Control Entertainment Time',
          'Block Apps',
          'Custom Blocking',
          'Priority Support',
          '17% savings compared to monthly',
        ],
        isPopular: true,
      ),
      SubscriptionPlan(
        id: 'yearly',
        name: 'Yearly Plan',
        description: 'Best value for long-term commitment',
        price: 3.0, // TODO: Replace with actual price
        durationInDays: 365,
        features: [
          'All premium features',
          'Porn Deaddiction Mode',
          'Block Adult Sites',
          'Control Gaming Time',
          'Control Entertainment Time',
          'Block Apps',
          'Custom Blocking',
          'Priority Support',
          '33% savings compared to monthly',
          'Exclusive yearly features',
        ],
        isPopular: false,
      ),
    ];
  }

  /// Get plan by ID
  SubscriptionPlan? getPlanById(String planId) {
    try {
      return getAvailablePlans().firstWhere((plan) => plan.id == planId);
    } catch (e) {
      return null;
    }
  }

  /// Get most popular plan
  SubscriptionPlan? getMostPopularPlan() {
    try {
      return getAvailablePlans().firstWhere((plan) => plan.isPopular);
    } catch (e) {
      return getAvailablePlans().isNotEmpty ? getAvailablePlans().first : null;
    }
  }

  /// Calculate savings compared to monthly plan
  double calculateSavings(SubscriptionPlan plan) {
    final monthlyPlan = getPlanById('monthly');
    if (monthlyPlan == null) return 0.0;

    final monthlyEquivalent = (plan.durationInDays / 30) * monthlyPlan.price;
    return monthlyEquivalent - plan.price;
  }

  /// Calculate savings percentage
  double calculateSavingsPercentage(SubscriptionPlan plan) {
    final monthlyPlan = getPlanById('monthly');
    if (monthlyPlan == null) return 0.0;

    final monthlyEquivalent = (plan.durationInDays / 30) * monthlyPlan.price;
    if (monthlyEquivalent == 0) return 0.0;

    return ((monthlyEquivalent - plan.price) / monthlyEquivalent) * 100;
  }

  /// Get formatted savings text
  String getSavingsText(SubscriptionPlan plan) {
    final savings = calculateSavings(plan);
    final percentage = calculateSavingsPercentage(plan);

    if (savings <= 0) return '';

    return 'Save ₹${savings.toStringAsFixed(0)} (${percentage.toStringAsFixed(0)}%)';
  }

  /// Validate plan selection
  bool isValidPlan(String planId) {
    return getPlanById(planId) != null;
  }

  /// Get plan features as formatted string
  String getFormattedFeatures(SubscriptionPlan plan) {
    return plan.features.join('\n• ');
  }

  /// Get plan comparison data
  Map<String, dynamic> getPlanComparison() {
    final plans = getAvailablePlans();
    final monthlyPlan = getPlanById('monthly');

    if (monthlyPlan == null) return {};

    return {
      'plans': plans
          .map((plan) => {
                'plan': plan,
                'savings': calculateSavings(plan),
                'savingsPercentage': calculateSavingsPercentage(plan),
                'monthlyEquivalent': plan.price / (plan.durationInDays / 30),
              })
          .toList(),
    };
  }
}
